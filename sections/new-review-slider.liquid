
{%- style -%}
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }
{%- endstyle -%}
<div class="new-review-slider section-{{ section.id }}-padding">
  <div class="section section-blends section-full text-custom">
    <div class="inew-review-slider-main">
      <div class="header-top">
        <div class="header-top-left">
          {% if section.settings.section_title != blank %}
          <div class="section-title">
            <h2>{{ section.settings.section_title }}</h2>
          </div>
          {% endif %}
        </div>
        <div class="header-top-right">
          {% if section.settings.section_description != blank %}
          <div class="section-description">
            {{ section.settings.section_description }}
          </div>
          {% endif %}
          {% if section.settings.section_review_title != blank %}
          <div class="section-review-title">
            <div>
              <svg width="146" height="22" viewBox="0 0 146 22" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M22.8479 8.53059C22.8479 8.20134 22.5967 7.99181 22.0943 7.90202L15.223 6.86936L12.1641 0.583651C11.9868 0.224466 11.7651 0.0448742 11.4991 0.0448742C11.2332 0.0448742 11.0115 0.224466 10.8342 0.583651L7.77533 6.86936L0.904019 7.90202C0.4016 7.99181 0.150391 8.20134 0.150391 8.53059C0.150391 8.74011 0.268607 8.9646 0.505039 9.20406L5.47012 14.0979L4.27318 21.0122C4.27318 21.1319 4.27318 21.2217 4.27318 21.2816C4.27318 21.4612 4.31751 21.6183 4.40617 21.753C4.49484 21.8877 4.64261 21.9551 4.84948 21.9551C4.99725 21.9551 5.17458 21.8952 5.38146 21.7755L11.4991 18.5428L17.6168 21.7755C17.8237 21.8952 18.001 21.9551 18.1488 21.9551C18.3557 21.9551 18.5035 21.8877 18.5921 21.753C18.6808 21.6183 18.7251 21.4612 18.7251 21.2816C18.7251 21.1619 18.7251 21.0721 18.7251 21.0122L17.5282 14.0979L22.4932 9.20406C22.7297 8.9646 22.8479 8.74011 22.8479 8.53059Z" fill="#FFA600"/>
              <path d="M53.5315 8.53059C53.5315 8.20134 53.2803 7.99181 52.7779 7.90202L45.9065 6.86936L42.8477 0.583651C42.6704 0.224466 42.4487 0.0448742 42.1827 0.0448742C41.9168 0.0448742 41.6951 0.224466 41.5178 0.583651L38.4589 6.86936L31.5876 7.90202C31.0852 7.99181 30.834 8.20134 30.834 8.53059C30.834 8.74011 30.9522 8.9646 31.1886 9.20406L36.1537 14.0979L34.9568 21.0122C34.9568 21.1319 34.9568 21.2217 34.9568 21.2816C34.9568 21.4612 35.0011 21.6183 35.0898 21.753C35.1784 21.8877 35.3262 21.9551 35.5331 21.9551C35.6808 21.9551 35.8582 21.8952 36.0651 21.7755L42.1827 18.5428L48.3004 21.7755C48.5073 21.8952 48.6846 21.9551 48.8324 21.9551C49.0393 21.9551 49.187 21.8877 49.2757 21.753C49.3644 21.6183 49.4087 21.4612 49.4087 21.2816C49.4087 21.1619 49.4087 21.0721 49.4087 21.0122L48.2118 14.0979L53.1768 9.20406C53.4133 8.9646 53.5315 8.74011 53.5315 8.53059Z" fill="#FFA600"/>
              <path d="M84.2151 8.53059C84.2151 8.20134 83.9639 7.99181 83.4615 7.90202L76.5901 6.86936L73.5313 0.583651C73.354 0.224466 73.1323 0.0448742 72.8663 0.0448742C72.6003 0.0448742 72.3787 0.224466 72.2014 0.583651L69.1425 6.86936L62.2712 7.90202C61.7688 7.99181 61.5176 8.20134 61.5176 8.53059C61.5176 8.74011 61.6358 8.9646 61.8722 9.20406L66.8373 14.0979L65.6404 21.0122C65.6404 21.1319 65.6404 21.2217 65.6404 21.2816C65.6404 21.4612 65.6847 21.6183 65.7734 21.753C65.862 21.8877 66.0098 21.9551 66.2167 21.9551C66.3644 21.9551 66.5418 21.8952 66.7486 21.7755L72.8663 18.5428L78.984 21.7755C79.1909 21.8952 79.3682 21.9551 79.516 21.9551C79.7229 21.9551 79.8706 21.8877 79.9593 21.753C80.048 21.6183 80.0923 21.4612 80.0923 21.2816C80.0923 21.1619 80.0923 21.0721 80.0923 21.0122L78.8954 14.0979L83.8604 9.20406C84.0969 8.9646 84.2151 8.74011 84.2151 8.53059Z" fill="#FFA600"/>
              <path d="M114.899 8.53059C114.899 8.20134 114.647 7.99181 114.145 7.90202L107.274 6.86936L104.215 0.583651C104.038 0.224466 103.816 0.0448742 103.55 0.0448742C103.284 0.0448742 103.062 0.224466 102.885 0.583651L99.8261 6.86936L92.9548 7.90202C92.4524 7.99181 92.2012 8.20134 92.2012 8.53059C92.2012 8.74011 92.3194 8.9646 92.5558 9.20406L97.5209 14.0979L96.324 21.0122C96.324 21.1319 96.324 21.2217 96.324 21.2816C96.324 21.4612 96.3683 21.6183 96.457 21.753C96.5456 21.8877 96.6934 21.9551 96.9003 21.9551C97.048 21.9551 97.2254 21.8952 97.4322 21.7755L103.55 18.5428L109.668 21.7755C109.874 21.8952 110.052 21.9551 110.2 21.9551C110.406 21.9551 110.554 21.8877 110.643 21.753C110.732 21.6183 110.776 21.4612 110.776 21.2816C110.776 21.1619 110.776 21.0721 110.776 21.0122L109.579 14.0979L114.544 9.20406C114.78 8.9646 114.899 8.74011 114.899 8.53059Z" fill="#FFA600"/>
              <path d="M145.582 8.53059C145.582 8.20134 145.331 7.99181 144.829 7.90202L137.957 6.86936L134.898 0.583651C134.721 0.224466 134.5 0.0448742 134.234 0.0448742C133.968 0.0448742 133.746 0.224466 133.569 0.583651L130.51 6.86936L123.638 7.90202C123.136 7.99181 122.885 8.20134 122.885 8.53059C122.885 8.74011 123.003 8.9646 123.239 9.20406L128.204 14.0979L127.008 21.0122C127.008 21.1319 127.008 21.2217 127.008 21.2816C127.008 21.4612 127.052 21.6183 127.141 21.753C127.229 21.8877 127.377 21.9551 127.584 21.9551C127.732 21.9551 127.909 21.8952 128.116 21.7755L134.234 18.5428L140.351 21.7755C140.558 21.8952 140.735 21.9551 140.883 21.9551C141.09 21.9551 141.238 21.8877 141.326 21.753C141.415 21.6183 141.459 21.4612 141.459 21.2816C141.459 21.1619 141.459 21.0721 141.459 21.0122L140.263 14.0979L145.228 9.20406C145.464 8.9646 145.582 8.74011 145.582 8.53059Z" fill="#FFA600"/>
              </svg>
              {{ section.settings.section_review_title }}
            </div>
          </div>
          {% endif %}
        </div>
      </div>
      {% if section.settings.enable_slider_buttons == true %}
      <div class="sub-title">
        <div class="image-section-card-carousel image-section-card-carousel-{{ section.id }}">
          {% for block in section.blocks %}
            {% if block.settings.section_card_description != blank %}
              <div class="carousel-item-top carousel-cell">
                <div class="section-button">
                {{ block.settings.section_card_description }}
                </div>
              </div>
            {% endif %}
           {% endfor %}
       </div>
      </div>
      {% endif %}
      <div class="new-review-slider-content">
     
       <div class="image-card-carousel image-card-carousel-{{ section.id }}">
        
       {% for block in section.blocks %}
          {%- case block.type -%}
          {%- when 'image-text-review' -%}
            <div class="carousel-item carousel-cell">
              {% if block.settings.card_image != blank %}
              <div class="card-image">
                <img src="{{ block.settings.card_image | img_url: 'master' }}" alt="{{ block.settings.card_image.alt }}" >
              </div>
              {% else %}
                {% if block.settings.video != blank %}
                  {{- block.settings.video | video_tag: playsinline: true, muted: true, loop: true, autoplay: true, controls: false, preload: 'none', class: 'video-element' -}}
                {% endif %}
              {% endif %}
             {% if block.settings.card_title != blank %}
              <div class="card-title">
                <h2>{{ block.settings.card_title }}</h2>
              </div>
              {% endif %}
             
             {% if block.settings.card_description != blank %}
              <div class="card-description">
                {{ block.settings.card_description }}
              </div>
              {% endif %}
              <div class="authore-Detail">
                <div class="authore-Detail-left">
                  {% if block.settings.authore_image != blank %}
                   <div class="authore-image">
                     <img src="{{ block.settings.authore_image | img_url: '60x' }}" alt="{{ block.settings.authore_image.alt }}" />
                   </div>
                  {% endif %}
                  <div class="authore-Detail-main">
                    {% if block.settings.authore_name != blank %}
                    <div class="authore-name">
                      {{ block.settings.authore_name }}
                    </div>
                    {% endif %}
                    {% if block.settings.authore_Verified_tag != blank %}
                    <div class="authore-Verified-tag">
                      {{ block.settings.authore_Verified_tag }}
                    </div>
                    {% endif %}
                  </div>
                </div>
                <div class="authore-Detail-right">
                 {% if block.settings.video != blank %} 
                  <div class="Video-button playing">
                    
                    <svg class="Video-button-play" width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M24.0013 0.666016C19.3864 0.666016 14.8752 2.03449 11.038 4.59839C7.20086 7.16229 4.21017 10.8065 2.44412 15.0701C0.678079 19.3337 0.216001 24.0252 1.11632 28.5515C2.01664 33.0777 4.23893 37.2353 7.50215 40.4985C10.7654 43.7617 14.923 45.984 19.4492 46.8843C23.9754 47.7847 28.667 47.3226 32.9306 45.5565C37.1942 43.7905 40.8384 40.7998 43.4023 36.9627C45.9662 33.1255 47.3346 28.6142 47.3346 23.9993C47.3346 20.9352 46.7311 17.901 45.5585 15.0701C44.3859 12.2391 42.6672 9.66689 40.5005 7.50019C38.3338 5.33349 35.7615 3.61477 32.9306 2.44216C30.0997 1.26955 27.0655 0.666016 24.0013 0.666016ZM19.3346 34.4993V13.4993L33.3346 23.9993L19.3346 34.4993Z" fill="black"/>
                    </svg>
                    <svg class="Video-button-push" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" height="48" width="48" version="1.1" id="Capa_1" viewBox="0 0 271.953 271.953" xml:space="preserve">
                    <g>
                    	<g>
                    		<path style="fill:#010002;" d="M135.977,271.953c75.097,0,135.977-60.879,135.977-135.977S211.074,0,135.977,0S0,60.879,0,135.977    S60.879,271.953,135.977,271.953z M135.977,21.756c62.979,0,114.22,51.241,114.22,114.22s-51.241,114.22-114.22,114.22    s-114.22-51.241-114.22-114.22S72.992,21.756,135.977,21.756z"/>
                    		<path style="fill:#010002;" d="M110.707,200.114c7.511,0,13.598-6.086,13.598-13.598V83.174c0-7.511-6.086-13.598-13.598-13.598    c-7.511,0-13.598,6.086-13.598,13.598v103.342C97.109,194.028,103.195,200.114,110.707,200.114z"/>
                    		<path style="fill:#010002;" d="M165.097,200.114c7.511,0,13.598-6.086,13.598-13.598V83.174c0-7.511-6.086-13.598-13.598-13.598    S151.5,75.663,151.5,83.174v103.342C151.5,194.028,157.586,200.114,165.097,200.114z"/>
                    	</g>
                    </g>
                    </svg>
                              
                  </div>
                   {% endif %}          
                </div>
              </div>
            </div>
          {%- endcase -%}
         {%- case block.type -%}
          {%- when 'text-review-one' -%}
            <div class="text-review-one carousel-item carousel-cell">
             <div class="content-top">
              {% if block.settings.card_top_title!= blank %}
              <div class="card-top-title">
                <h2>{{ block.settings.card_top_title }}</h2>
              </div>
              {% endif %}
               {% if block.settings.card_description != blank %}
              <div class="card-description">
                {{ block.settings.card_description }}
              </div>
              {% endif %}
             </div>
            <div class="content-bottom">
             {% if block.settings.card_title != blank %}
              <div class="card-title">
                <div class="card-title-icon">
                 <svg width="146" height="22" viewBox="0 0 146 22" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M22.8479 8.53059C22.8479 8.20134 22.5967 7.99181 22.0943 7.90202L15.223 6.86936L12.1641 0.583651C11.9868 0.224466 11.7651 0.0448742 11.4991 0.0448742C11.2332 0.0448742 11.0115 0.224466 10.8342 0.583651L7.77533 6.86936L0.904019 7.90202C0.4016 7.99181 0.150391 8.20134 0.150391 8.53059C0.150391 8.74011 0.268607 8.9646 0.505039 9.20406L5.47012 14.0979L4.27318 21.0122C4.27318 21.1319 4.27318 21.2217 4.27318 21.2816C4.27318 21.4612 4.31751 21.6183 4.40617 21.753C4.49484 21.8877 4.64261 21.9551 4.84948 21.9551C4.99725 21.9551 5.17458 21.8952 5.38146 21.7755L11.4991 18.5428L17.6168 21.7755C17.8237 21.8952 18.001 21.9551 18.1488 21.9551C18.3557 21.9551 18.5035 21.8877 18.5921 21.753C18.6808 21.6183 18.7251 21.4612 18.7251 21.2816C18.7251 21.1619 18.7251 21.0721 18.7251 21.0122L17.5282 14.0979L22.4932 9.20406C22.7297 8.9646 22.8479 8.74011 22.8479 8.53059Z" fill="#FFA600"/>
                  <path d="M53.5315 8.53059C53.5315 8.20134 53.2803 7.99181 52.7779 7.90202L45.9065 6.86936L42.8477 0.583651C42.6704 0.224466 42.4487 0.0448742 42.1827 0.0448742C41.9168 0.0448742 41.6951 0.224466 41.5178 0.583651L38.4589 6.86936L31.5876 7.90202C31.0852 7.99181 30.834 8.20134 30.834 8.53059C30.834 8.74011 30.9522 8.9646 31.1886 9.20406L36.1537 14.0979L34.9568 21.0122C34.9568 21.1319 34.9568 21.2217 34.9568 21.2816C34.9568 21.4612 35.0011 21.6183 35.0898 21.753C35.1784 21.8877 35.3262 21.9551 35.5331 21.9551C35.6808 21.9551 35.8582 21.8952 36.0651 21.7755L42.1827 18.5428L48.3004 21.7755C48.5073 21.8952 48.6846 21.9551 48.8324 21.9551C49.0393 21.9551 49.187 21.8877 49.2757 21.753C49.3644 21.6183 49.4087 21.4612 49.4087 21.2816C49.4087 21.1619 49.4087 21.0721 49.4087 21.0122L48.2118 14.0979L53.1768 9.20406C53.4133 8.9646 53.5315 8.74011 53.5315 8.53059Z" fill="#FFA600"/>
                  <path d="M84.2151 8.53059C84.2151 8.20134 83.9639 7.99181 83.4615 7.90202L76.5901 6.86936L73.5313 0.583651C73.354 0.224466 73.1323 0.0448742 72.8663 0.0448742C72.6003 0.0448742 72.3787 0.224466 72.2014 0.583651L69.1425 6.86936L62.2712 7.90202C61.7688 7.99181 61.5176 8.20134 61.5176 8.53059C61.5176 8.74011 61.6358 8.9646 61.8722 9.20406L66.8373 14.0979L65.6404 21.0122C65.6404 21.1319 65.6404 21.2217 65.6404 21.2816C65.6404 21.4612 65.6847 21.6183 65.7734 21.753C65.862 21.8877 66.0098 21.9551 66.2167 21.9551C66.3644 21.9551 66.5418 21.8952 66.7486 21.7755L72.8663 18.5428L78.984 21.7755C79.1909 21.8952 79.3682 21.9551 79.516 21.9551C79.7229 21.9551 79.8706 21.8877 79.9593 21.753C80.048 21.6183 80.0923 21.4612 80.0923 21.2816C80.0923 21.1619 80.0923 21.0721 80.0923 21.0122L78.8954 14.0979L83.8604 9.20406C84.0969 8.9646 84.2151 8.74011 84.2151 8.53059Z" fill="#FFA600"/>
                  <path d="M114.899 8.53059C114.899 8.20134 114.647 7.99181 114.145 7.90202L107.274 6.86936L104.215 0.583651C104.038 0.224466 103.816 0.0448742 103.55 0.0448742C103.284 0.0448742 103.062 0.224466 102.885 0.583651L99.8261 6.86936L92.9548 7.90202C92.4524 7.99181 92.2012 8.20134 92.2012 8.53059C92.2012 8.74011 92.3194 8.9646 92.5558 9.20406L97.5209 14.0979L96.324 21.0122C96.324 21.1319 96.324 21.2217 96.324 21.2816C96.324 21.4612 96.3683 21.6183 96.457 21.753C96.5456 21.8877 96.6934 21.9551 96.9003 21.9551C97.048 21.9551 97.2254 21.8952 97.4322 21.7755L103.55 18.5428L109.668 21.7755C109.874 21.8952 110.052 21.9551 110.2 21.9551C110.406 21.9551 110.554 21.8877 110.643 21.753C110.732 21.6183 110.776 21.4612 110.776 21.2816C110.776 21.1619 110.776 21.0721 110.776 21.0122L109.579 14.0979L114.544 9.20406C114.78 8.9646 114.899 8.74011 114.899 8.53059Z" fill="#FFA600"/>
                  <path d="M145.582 8.53059C145.582 8.20134 145.331 7.99181 144.829 7.90202L137.957 6.86936L134.898 0.583651C134.721 0.224466 134.5 0.0448742 134.234 0.0448742C133.968 0.0448742 133.746 0.224466 133.569 0.583651L130.51 6.86936L123.638 7.90202C123.136 7.99181 122.885 8.20134 122.885 8.53059C122.885 8.74011 123.003 8.9646 123.239 9.20406L128.204 14.0979L127.008 21.0122C127.008 21.1319 127.008 21.2217 127.008 21.2816C127.008 21.4612 127.052 21.6183 127.141 21.753C127.229 21.8877 127.377 21.9551 127.584 21.9551C127.732 21.9551 127.909 21.8952 128.116 21.7755L134.234 18.5428L140.351 21.7755C140.558 21.8952 140.735 21.9551 140.883 21.9551C141.09 21.9551 141.238 21.8877 141.326 21.753C141.415 21.6183 141.459 21.4612 141.459 21.2816C141.459 21.1619 141.459 21.0721 141.459 21.0122L140.263 14.0979L145.228 9.20406C145.464 8.9646 145.582 8.74011 145.582 8.53059Z" fill="#FFA600"/>
                  </svg>
                </div>
                <h2>{{ block.settings.card_title }}</h2>
              </div>
              {% endif %}
             
            
              <div class="authore-Detail">
                <div class="authore-Detail-left">
                  {% if block.settings.authore_image != blank %}
                   <div class="authore-image">
                     <img src="{{ block.settings.authore_image | img_url: '60x' }}" alt="{{ block.settings.authore_image.alt }}" />
                   </div>
                  {% endif %}
                  <div class="authore-Detail-main">
                    {% if block.settings.authore_name != blank %}
                    <div class="authore-name">
                      {{ block.settings.authore_name }}
                    </div>
                    {% endif %}
                    {% if block.settings.authore_Verified_tag != blank %}
                    <div class="authore-Verified-tag">
                      {{ block.settings.authore_Verified_tag }}
                    </div>
                    {% endif %}
                  </div>
                </div>
                <div class="authore-Detail-right">
                  <!-- <div class="Video-button">
                    <svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M24.0013 0.666016C19.3864 0.666016 14.8752 2.03449 11.038 4.59839C7.20086 7.16229 4.21017 10.8065 2.44412 15.0701C0.678079 19.3337 0.216001 24.0252 1.11632 28.5515C2.01664 33.0777 4.23893 37.2353 7.50215 40.4985C10.7654 43.7617 14.923 45.984 19.4492 46.8843C23.9754 47.7847 28.667 47.3226 32.9306 45.5565C37.1942 43.7905 40.8384 40.7998 43.4023 36.9627C45.9662 33.1255 47.3346 28.6142 47.3346 23.9993C47.3346 20.9352 46.7311 17.901 45.5585 15.0701C44.3859 12.2391 42.6672 9.66689 40.5005 7.50019C38.3338 5.33349 35.7615 3.61477 32.9306 2.44216C30.0997 1.26955 27.0655 0.666016 24.0013 0.666016ZM19.3346 34.4993V13.4993L33.3346 23.9993L19.3346 34.4993Z" fill="black"/>
                    </svg>
                  </div> -->
                </div>
              </div>
            </div>
            </div>
          {%- endcase -%}
         {%- case block.type -%}
          {%- when 'text-review-two' -%}
            <div class="text-review-two carousel-item carousel-cell">
             <div class="content-top">
              {% if block.settings.card_top_title!= blank %}
              <div class="card-top-title">
                <div class="card-title-icon">
                 <svg width="146" height="22" viewBox="0 0 146 22" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M22.8479 8.53059C22.8479 8.20134 22.5967 7.99181 22.0943 7.90202L15.223 6.86936L12.1641 0.583651C11.9868 0.224466 11.7651 0.0448742 11.4991 0.0448742C11.2332 0.0448742 11.0115 0.224466 10.8342 0.583651L7.77533 6.86936L0.904019 7.90202C0.4016 7.99181 0.150391 8.20134 0.150391 8.53059C0.150391 8.74011 0.268607 8.9646 0.505039 9.20406L5.47012 14.0979L4.27318 21.0122C4.27318 21.1319 4.27318 21.2217 4.27318 21.2816C4.27318 21.4612 4.31751 21.6183 4.40617 21.753C4.49484 21.8877 4.64261 21.9551 4.84948 21.9551C4.99725 21.9551 5.17458 21.8952 5.38146 21.7755L11.4991 18.5428L17.6168 21.7755C17.8237 21.8952 18.001 21.9551 18.1488 21.9551C18.3557 21.9551 18.5035 21.8877 18.5921 21.753C18.6808 21.6183 18.7251 21.4612 18.7251 21.2816C18.7251 21.1619 18.7251 21.0721 18.7251 21.0122L17.5282 14.0979L22.4932 9.20406C22.7297 8.9646 22.8479 8.74011 22.8479 8.53059Z" fill="#FFA600"/>
                  <path d="M53.5315 8.53059C53.5315 8.20134 53.2803 7.99181 52.7779 7.90202L45.9065 6.86936L42.8477 0.583651C42.6704 0.224466 42.4487 0.0448742 42.1827 0.0448742C41.9168 0.0448742 41.6951 0.224466 41.5178 0.583651L38.4589 6.86936L31.5876 7.90202C31.0852 7.99181 30.834 8.20134 30.834 8.53059C30.834 8.74011 30.9522 8.9646 31.1886 9.20406L36.1537 14.0979L34.9568 21.0122C34.9568 21.1319 34.9568 21.2217 34.9568 21.2816C34.9568 21.4612 35.0011 21.6183 35.0898 21.753C35.1784 21.8877 35.3262 21.9551 35.5331 21.9551C35.6808 21.9551 35.8582 21.8952 36.0651 21.7755L42.1827 18.5428L48.3004 21.7755C48.5073 21.8952 48.6846 21.9551 48.8324 21.9551C49.0393 21.9551 49.187 21.8877 49.2757 21.753C49.3644 21.6183 49.4087 21.4612 49.4087 21.2816C49.4087 21.1619 49.4087 21.0721 49.4087 21.0122L48.2118 14.0979L53.1768 9.20406C53.4133 8.9646 53.5315 8.74011 53.5315 8.53059Z" fill="#FFA600"/>
                  <path d="M84.2151 8.53059C84.2151 8.20134 83.9639 7.99181 83.4615 7.90202L76.5901 6.86936L73.5313 0.583651C73.354 0.224466 73.1323 0.0448742 72.8663 0.0448742C72.6003 0.0448742 72.3787 0.224466 72.2014 0.583651L69.1425 6.86936L62.2712 7.90202C61.7688 7.99181 61.5176 8.20134 61.5176 8.53059C61.5176 8.74011 61.6358 8.9646 61.8722 9.20406L66.8373 14.0979L65.6404 21.0122C65.6404 21.1319 65.6404 21.2217 65.6404 21.2816C65.6404 21.4612 65.6847 21.6183 65.7734 21.753C65.862 21.8877 66.0098 21.9551 66.2167 21.9551C66.3644 21.9551 66.5418 21.8952 66.7486 21.7755L72.8663 18.5428L78.984 21.7755C79.1909 21.8952 79.3682 21.9551 79.516 21.9551C79.7229 21.9551 79.8706 21.8877 79.9593 21.753C80.048 21.6183 80.0923 21.4612 80.0923 21.2816C80.0923 21.1619 80.0923 21.0721 80.0923 21.0122L78.8954 14.0979L83.8604 9.20406C84.0969 8.9646 84.2151 8.74011 84.2151 8.53059Z" fill="#FFA600"/>
                  <path d="M114.899 8.53059C114.899 8.20134 114.647 7.99181 114.145 7.90202L107.274 6.86936L104.215 0.583651C104.038 0.224466 103.816 0.0448742 103.55 0.0448742C103.284 0.0448742 103.062 0.224466 102.885 0.583651L99.8261 6.86936L92.9548 7.90202C92.4524 7.99181 92.2012 8.20134 92.2012 8.53059C92.2012 8.74011 92.3194 8.9646 92.5558 9.20406L97.5209 14.0979L96.324 21.0122C96.324 21.1319 96.324 21.2217 96.324 21.2816C96.324 21.4612 96.3683 21.6183 96.457 21.753C96.5456 21.8877 96.6934 21.9551 96.9003 21.9551C97.048 21.9551 97.2254 21.8952 97.4322 21.7755L103.55 18.5428L109.668 21.7755C109.874 21.8952 110.052 21.9551 110.2 21.9551C110.406 21.9551 110.554 21.8877 110.643 21.753C110.732 21.6183 110.776 21.4612 110.776 21.2816C110.776 21.1619 110.776 21.0721 110.776 21.0122L109.579 14.0979L114.544 9.20406C114.78 8.9646 114.899 8.74011 114.899 8.53059Z" fill="#FFA600"/>
                  <path d="M145.582 8.53059C145.582 8.20134 145.331 7.99181 144.829 7.90202L137.957 6.86936L134.898 0.583651C134.721 0.224466 134.5 0.0448742 134.234 0.0448742C133.968 0.0448742 133.746 0.224466 133.569 0.583651L130.51 6.86936L123.638 7.90202C123.136 7.99181 122.885 8.20134 122.885 8.53059C122.885 8.74011 123.003 8.9646 123.239 9.20406L128.204 14.0979L127.008 21.0122C127.008 21.1319 127.008 21.2217 127.008 21.2816C127.008 21.4612 127.052 21.6183 127.141 21.753C127.229 21.8877 127.377 21.9551 127.584 21.9551C127.732 21.9551 127.909 21.8952 128.116 21.7755L134.234 18.5428L140.351 21.7755C140.558 21.8952 140.735 21.9551 140.883 21.9551C141.09 21.9551 141.238 21.8877 141.326 21.753C141.415 21.6183 141.459 21.4612 141.459 21.2816C141.459 21.1619 141.459 21.0721 141.459 21.0122L140.263 14.0979L145.228 9.20406C145.464 8.9646 145.582 8.74011 145.582 8.53059Z" fill="#FFA600"/>
                  </svg>
                </div>
                <h2>{{ block.settings.card_top_title }}</h2>
              </div>
              {% endif %}
               {% if block.settings.card_description != blank %}
              <div class="card-description">
                {{ block.settings.card_description }}
              </div>
              {% endif %}
             </div>
            <div class="content-bottom">
             {% if block.settings.card_title != blank %}
              <div class="card-title">
                
                <h2>{{ block.settings.card_title }}</h2>
              </div>
              {% endif %}
             
            
              <div class="authore-Detail">
                <div class="authore-Detail-left">
                  {% if block.settings.authore_image != blank %}
                   <div class="authore-image">
                     <img src="{{ block.settings.authore_image | img_url: '60x' }}" alt="{{ block.settings.authore_image.alt }}" />
                   </div>
                  {% endif %}
                  <div class="authore-Detail-main">
                    {% if block.settings.authore_name != blank %}
                    <div class="authore-name">
                      {{ block.settings.authore_name }}
                    </div>
                    {% endif %}
                    {% if block.settings.authore_Verified_tag != blank %}
                    <div class="authore-Verified-tag">
                      {{ block.settings.authore_Verified_tag }}
                    </div>
                    {% endif %}
                  </div>
                </div>
                <div class="authore-Detail-right">
                  <!-- <div class="Video-button">
                    <svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M24.0013 0.666016C19.3864 0.666016 14.8752 2.03449 11.038 4.59839C7.20086 7.16229 4.21017 10.8065 2.44412 15.0701C0.678079 19.3337 0.216001 24.0252 1.11632 28.5515C2.01664 33.0777 4.23893 37.2353 7.50215 40.4985C10.7654 43.7617 14.923 45.984 19.4492 46.8843C23.9754 47.7847 28.667 47.3226 32.9306 45.5565C37.1942 43.7905 40.8384 40.7998 43.4023 36.9627C45.9662 33.1255 47.3346 28.6142 47.3346 23.9993C47.3346 20.9352 46.7311 17.901 45.5585 15.0701C44.3859 12.2391 42.6672 9.66689 40.5005 7.50019C38.3338 5.33349 35.7615 3.61477 32.9306 2.44216C30.0997 1.26955 27.0655 0.666016 24.0013 0.666016ZM19.3346 34.4993V13.4993L33.3346 23.9993L19.3346 34.4993Z" fill="black"/>
                    </svg>
                  </div> -->
                </div>
                
              </div>
            </div>
            </div>
          {%- endcase -%}
        {% endfor %}
        
        </div>
      </div>
      {% if section.settings.review_footer != blank %}
      <div class="review-footer">
        {{ section.settings.review_footer }}
      </div>
      {% endif %}              
    </div>
  </div>
</div>
<script>
$(document).ready(function(){
  {% if section.settings.enable_slider_buttons == true %}
  $('.image-section-card-carousel-{{ section.id }}').flickity({
    // options
    cellAlign: 'left',
    contain: true,
    imagesLoaded: true,
    pageDots: false,
    wrapAround: false,
    draggable: true,
    adaptiveHeight: true,
    asNavFor: '.image-card-carousel-{{ section.id }}'
  });
  {% endif %}
  $('.image-card-carousel-{{ section.id }}').flickity({
    // options
    cellAlign: 'left',
    contain: true,
    imagesLoaded: true,
    pageDots: false,
    wrapAround: true
  });
  
});

document.addEventListener("DOMContentLoaded", function () {
  var buttons = document.querySelectorAll(".new-review-slider.section-{{ section.id }}-padding .Video-button");
  buttons.forEach(function (button) {
    button.addEventListener("click", function () {
      var container = button.closest(".carousel-item");
      var video = container.querySelector("video.video-element");
      if (!video) return;
      if (video.paused) {
        video.play();
        button.classList.add("playing");
      } else {
        video.pause();
        button.classList.remove("playing");
      }
    });
  });
});

</script>

<style>

svg.Video-button-play {
    display: block;
}
.Video-button.playing svg.Video-button-play,
.Video-button-push {
    display: none;
}
.Video-button.playing .Video-button-push {
    display: block;
}
.Video-button {
    cursor: pointer;
}
.carousel-item+.carousel-item,
.carousel-cell+.carousel-cell{
    display: none;
}
.flickity-enabled .carousel-item+.carousel-item,
.flickity-enabled .carousel-cell+.carousel-cell {
    display: block;
}
  
.section-review-title {
    display: flex;
    justify-content: end;
}
.new-review-slider .header-top {
    display: flex;
    gap: 40px;
}
.new-review-slider .header-top-right,
.new-review-slider .header-top-left {
    width: 50%;
}
.new-review-slider .card-image {
  position: relative;
  overflow: hidden;
  border-radius: 10px;
      aspect-ratio: 5 / 6;
}
.new-review-slider .card-image img {
    display: block;
    width: 100%;
    height: 100%;
} 
.new-review-slider .carousel-item video.video-element {
    aspect-ratio: 5 / 6;
    /* object-fit: cover; */
  background: #000;
    overflow: hidden;
    border-radius: 10px;
}
{% if section.settings.image_ratio == 'square' %}
.new-review-slider .card-image {
  padding-bottom: 100%;
}
.new-review-slider .card-image img {
    position: absolute;
    object-fit: cover;
} 
{% elsif section.settings.image_ratio == 'portrait'  %}
.new-review-slider .card-image {
  padding-bottom: 120%;
}
.new-review-slider .card-image img {
    position: absolute;
    object-fit: cover;
} 
{% elsif section.settings.image_ratio == 'landscape'  %}
.new-review-slider .card-image {
  padding-bottom: 60%;
}
.new-review-slider .card-image img {
    position: absolute;
    object-fit: cover;
} 
{% else %}
{% endif %}}




  
</style>


<style>
.image-section-card-carousel .flickity-button {
  display: none;
} 
.flickity-button {
    position: absolute;
    background: #121212;
    border: none;
    color: #FFF;
}
.flickity-button:hover {
    background: #121212;
    cursor: pointer;
}
.flickity-button:focus {
    outline: 0;
    box-shadow: none;
}
.new-review-slider .carousel-item {
  width: 30%;
  padding: 10px;
  border: 2px solid #241F23;
  border-radius: 10px;
  margin-right: 10px !important;
}

.new-review-slider .section-title {
  padding-bottom: 20px;
}
.new-review-slider .section-title h2 {
  font-family: var(--heading-font-family);
  font-weight: 700;
  font-size: 62px;
  line-height: 100%;
  letter-spacing: 0px;
  vertical-align: middle;
  text-transform: uppercase;
 
}
.new-review-slider .sub-title {
    display: block;
    width: 100%;
    position: relative;
  /* max-width: 50%; */
   padding-bottom: 30px;
}
.new-review-slider .section-description {
    max-width: 100%;
    display: flex;
    column-gap: 10px;
}
.new-review-slider .section-description::before {
  content: url(https://cdn.shopify.com/s/files/1/0878/9170/6174/files/Vector.svg?v=1750574682);
}
.new-review-slider .image-card-slider-content {
    padding-top: 30px;
}
.new-review-slider .card-title h2 {
    padding-top: 20px;
    font-family: var(--heading-font-family);
    font-weight: 700;
    font-size: 30px;
    line-height: 124%;
    letter-spacing: .4px;
    vertical-align: middle;
    text-transform: uppercase;
    
}
.new-review-slider .section-description p,
.new-review-slider .card-description p {
    font-family: Meltmino;
    font-weight: 400;
    font-size: 20px;
    line-height: 23.4px;
    letter-spacing: 0px;
    vertical-align: middle;
    text-transform: uppercase;
    color: #2A2A2A;
}
.new-review-slider .section-review-title p {
    font-family: var(--heading-font-family);
    font-weight: 500;
    font-size: 24px;
    line-height: 25.1px;
    letter-spacing: 1px;
    vertical-align: middle;
    text-transform: uppercase;
    color: #2A2A2A;
}
.new-review-slider .authore-Detail {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 0
}
.new-review-slider .authore-Detail-left {
    display: flex;
    align-items: center;
    gap: 10px;
}
.new-review-slider .authore-image img {
    border-radius: 5px;
}
.new-review-slider .authore-name p {
    font-family: var(--heading-font-family);
    font-weight: 400;
    font-size: 20px;
    line-height: 20.16px;
    letter-spacing: 0px;
    color: #2A2A2A;
}
.new-review-slider .authore-Verified-tag p {
    background: #2a2a2a;
    margin-top: 5px;
    color: #FFFFFF;
    font-family: var(--heading-font-family);
    font-weight: 400;
    font-size: 12px;
    line-height: 16.13px;
    letter-spacing: 1px;
    padding: 3px 8px;
    border-radius: 2px;
    display: inline-block;
}
.new-review-slider .section-button p {
    font-family: Meltmino;
    font-weight: 400;
    font-size: 18px;
    line-height: 28.8px;
    letter-spacing: 0px;
    text-align: center;
    vertical-align: bottom;
    text-transform: uppercase
}
.new-review-slider .flickity-enabled .text-review-one.carousel-item.carousel-cell {
    background: #000;
    color: #FFF;
    min-height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}
.new-review-slider .text-review-one .authore-name p {
    color: #FFFFFF;
}
.new-review-slider .text-review-one .card-title h2 {
    padding-top: 0px;
}
.new-review-slider .text-review-one .card-description p {
   color: #FFFFFF;
  font-family: Meltmino;
  font-weight: 400;
  font-size: 16px;
  line-height: 22.4px;
  letter-spacing: 0px;
  text-transform: inherit;
}
.new-review-slider .text-review-one .card-top-title h2 {
  font-family: Meltmino;
  font-weight: 700;
  font-size: 45px;
  line-height: 100%;
  letter-spacing: 0px;
  text-transform: uppercase;
  padding-bottom: 16px;
  padding-top: 30px;
}
.new-review-slider .carousel-item-top.carousel-cell.is-selected.is-nav-selected .section-button {
    background: #2a2a2a;
    color: #FFFFFF;
}

.new-review-slider .section-button {
    border: 1px solid;
    padding: 10px 20px;
    border-radius: 12px;
    margin-right: 20px;
}
.new-review-slider .flickity-enabled .text-review-two.carousel-item.carousel-cell {
    min-height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 30px;
}
.new-review-slider .text-review-two.carousel-item.carousel-cell .card-top-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
      padding-bottom: 16px;
}
.new-review-slider .text-review-two .card-description p {
  font-family: Meltmino;
  font-weight: 400;
  font-size: 24px;
  line-height: 32px;
  letter-spacing: 0px;
  text-transform: inherit;
}
.new-review-slider .text-review-two .content-top {
    padding-bottom: 20px;
}
.new-review-slider .text-review-two .card-top-title h2 {
  font-family: var(--heading-font-family);
  font-weight: 400;
  font-size: 24px;
  line-height: 20.16px;
  letter-spacing: 1px;
}
.new-review-slider .review-footer {
    padding-top: 30px;
}
.new-review-slider span.review-footer-left {
  font-family: Meltmino;
  font-weight: 500;
  font-size: 20px;
  line-height: 28px;
  letter-spacing: 0px;
  vertical-align: middle;
  text-transform: uppercase;
  color: rgb(var(--text-color));
}
.new-review-slider span.review-footer-right {
  font-family: Meltmino;
  font-weight: 500;
  font-size: 20px;
  line-height: 28px;
  letter-spacing: 0px;
  vertical-align: middle;
  text-transform: uppercase;
  color: #888987;
}
@media only screen and (min-width: 1025px) and (max-width: 1400px) {
   .new-review-slider .carousel-item {
    width: 40%;
  }
}
 @media only screen and (min-width: 769px) and (max-width: 1024px) {
  .new-review-slider .carousel-item {
    width: 40%;
  }
   .new-review-slider .section-description p, 
   .new-review-slider .card-description p {
    font-size: 18px;
   }
.new-review-slider .header-top {
    flex-direction: column;
    gap: 10px;
}   
.new-review-slider .header-top-right, .new-review-slider .header-top-left {
    width: 100%;
}
.header-top-right {
    padding-bottom: 20px;
}
.new-review-slider .text-review-two.carousel-item.carousel-cell .card-top-title {
    flex-direction: column;
}
.new-review-slider .flickity-enabled .text-review-two.carousel-item.carousel-cell {
    padding: 30px 15px;
}   
.new-review-slider .text-review-two .card-description p {
    font-size: 18px;
}
}
  
 @media only screen and (min-width: 481px) and (max-width: 768px) {
.section-review-title {
    display: flex;
    justify-content: flex-start;
}
.new-review-slider .header-top {
  flex-direction: column;
  gap: 0px;
  padding-bottom: 25px;
}
   .new-review-slider .header-top-right,
.new-review-slider .header-top-left {
    width: 100%;
}
   .new-review-slider .carousel-item {
    width: 70%;
   }
   .new-review-slider .section-description {
    max-width: 100%;
   }
   .new-review-slider .section-title h2 {
     font-size: 45px;
   }
   .new-review-slider .section-description p, 
   .new-review-slider .card-description p {
    font-size: 18px;
   }
.new-review-slider .sub-title {
    max-width: 100%;
}
 }
  
 @media only screen and (min-width: 360px) and (max-width: 480px) {
   .card-title-icon svg {
    max-width: 80%;
}
  .new-review-slider .text-review-two .card-top-title h2,
.new-review-slider .text-review-two .card-description p {
  font-size: 18px;
  font-weight: 400;
}
.section-review-title {
display: flex;
justify-content: flex-start;
}
.new-review-slider .header-top {
flex-direction: column;
gap: 0px;
padding-bottom: 25px;
}
.new-review-slider .header-top-right,
.new-review-slider .header-top-left {
width: 100%;
}
.new-review-slider .carousel-item {
width: 90%;
}
.new-review-slider .section-description {
max-width: 100%;
}
.new-review-slider .section-title h2 {
font-size: 30px;
}
.new-review-slider .section-description p, 
.new-review-slider .card-description p {
font-size: 12px;
}
.new-review-slider .card-title h2 {
font-size: 20px;
}
.new-review-slider .sub-title {
max-width: 100%;
padding-bottom: 20px;
}
.new-review-slider .text-review-two.carousel-item.carousel-cell {
width: 100%;
}
 }
 
</style>
{% schema %}
  {
    "name": "New Review Slider",
    "settings": [
    {
      "type": "text",
      "id": "section_title",
      "label": "Heading"
    },
    {
      "type": "richtext",
      "id": "section_description",
      "label": "Description"
    },
    {
      "type": "richtext",
      "id": "section_review_title",
      "label": "Review Title"
    },
    {
      "type": "select",
      "id": "image_ratio",
      "options": [
        {
          "value": "adapt_image",
          "label": "Adapt to image"
        },
        {
          "value": "square",
          "label": "Square"
        },
        {
          "value": "portrait",
          "label": "Portrait"
        },
        {
          "value": "landscape",
          "label": "Landscape"
        }
      ],
      "label": "Image ratio",
      "default": "portrait"
    },
    {
      "type": "header",
      "content": "Section Padding"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Padding Top",
      "default": 40
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Padding Bottom",
      "default": 52
    },
    {
      "type": "textarea",
      "id": "review_footer",
      "label": "Review Footer"
    },
    {
      "type": "checkbox",
      "id": "enable_slider_buttons",
      "label": "Enable slider buttons",
      "default": false
    }
    ],
    "blocks": [
    {
      "type": "image-text-review",
      "name": "Image Text Review",
      "limit": 12,
      "settings": [
        {
          "type": "image_picker",
          "id": "card_image",
          "label": "Image"
        },
         {
          "type": "video",
          "id": "video",
          "label": "Video"
        },
        {
          "type": "text",
          "id": "card_title",
          "label": "Heading"
        },
        {
          "type": "richtext",
          "id": "card_description",
          "label": "Short Description"
        },
        {
          "type": "richtext",
          "id": "section_card_description",
          "label": "Button"
        },
        {
          "type": "image_picker",
          "id": "authore_image",
          "label": "Authore Image"
        },
        {
          "type": "richtext",
          "id": "authore_name",
          "label": "Authore Name"
        },
        {
          "type": "richtext",
          "id": "authore_Verified_tag",
          "label": "Verified Tag"
        }
      ]
    },
    {
      "type": "text-review-one",
      "name": "Text Review One",
      "limit": 12,
      "settings": [
        {
          "type": "text",
          "id": "card_top_title",
          "label": "Top Heading"
        },
        {
          "type": "richtext",
          "id": "card_description",
          "label": "Short Description"
        },
        {
          "type": "text",
          "id": "card_title",
          "label": "Heading"
        },
        {
          "type": "richtext",
          "id": "section_card_description",
          "label": "Button"
        },
        {
          "type": "image_picker",
          "id": "authore_image",
          "label": "Authore Image"
        },
        {
          "type": "richtext",
          "id": "authore_name",
          "label": "Authore Name"
        },
        {
          "type": "richtext",
          "id": "authore_Verified_tag",
          "label": "Verified Tag"
        }
      ]
    },
     {
      "type": "text-review-two",
      "name": "Text Review Two",
      "limit": 12,
      "settings": [
        {
          "type": "text",
          "id": "card_top_title",
          "label": "Top Heading"
        },
        {
          "type": "richtext",
          "id": "card_description",
          "label": "Short Description"
        },
        {
          "type": "text",
          "id": "card_title",
          "label": "Heading"
        },
        {
          "type": "richtext",
          "id": "section_card_description",
          "label": "Button"
        },
        {
          "type": "image_picker",
          "id": "authore_image",
          "label": "Authore Image"
        },
        {
          "type": "richtext",
          "id": "authore_name",
          "label": "Authore Name"
        },
        {
          "type": "richtext",
          "id": "authore_Verified_tag",
          "label": "Verified Tag"
        }
      ]
    }
    ],
    "presets": [
    {
      "name": "New Review Slider",
      "blocks": [
      {
        "type": "image-text-review"
      },
      {
        "type": "text-review-one"
      },
      {
        "type": "text-review-two"
      }
      ]
    }
  ]
  }
{% endschema %}