
{%- style -%}
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }
{%- endstyle -%}
<div class="image-card-slider section-{{ section.id }}-padding">
  <div class="section section-blends section-full text-custom">
    <div class="image-card-slider-main">
      {% if section.settings.section_title != blank %}
      <div class="section-title">
        <h2>{{ section.settings.section_title }}</h2>
      </div>
      {% endif %}
      <div class="sub-title">
        <div class="image-section-card-carousel image-section-card-carousel-{{ section.id }}">
          {% for block in section.blocks %}
            {% if block.settings.section_card_description != blank %}
              <div class="carousel-item-top carousel-cell">
                <div class="section-description">
                {{ block.settings.section_card_description }}
                </div>
              </div>
            {% endif %}
           {% endfor %}
       </div>
      </div>
      <div class="image-card-slider-content">
     
       <div class="image-card-carousel image-card-carousel-{{ section.id }}">
        
       {% for block in section.blocks %}
          <div class="carousel-item carousel-cell">
           
            {% if block.settings.card_image != blank %}
            <div class="card-image">
              <img src="{{ block.settings.card_image | img_url: 'master' }}" alt="{{ block.settings.card_image.alt }}" >
            </div>
            {% endif %}
           {% if block.settings.card_title != blank %}
            <div class="card-title">
              <h2>{{ block.settings.card_title }}</h2>
            </div>
            {% endif %}
           
           {% if block.settings.card_description != blank %}
            <div class="card-description">
              {{ block.settings.card_description }}
            </div>
            {% endif %}
            
          </div>
        {% endfor %}
        
        </div>
      </div>
    </div>
  </div>
</div>
<script>
$(document).ready(function(){
  $('.image-section-card-carousel-{{ section.id }}').flickity({
    // options
    cellAlign: 'left',
    contain: true,
    imagesLoaded: true,
    pageDots: false,
    wrapAround: true,
    draggable: false,
    adaptiveHeight: true,
    asNavFor: '.image-card-carousel-{{ section.id }}'
  });
  $('.image-card-carousel-{{ section.id }}').flickity({
    // options
    cellAlign: 'left',
    contain: true,
    imagesLoaded: true,
    pageDots: false,
    wrapAround: true
  });
  
});
</script>

<style>
.image-card-slider .card-image {
  position: relative;
  overflow: hidden;
  border-radius: 10px;
}
.image-card-slider .card-image img {
    display: block;
    width: 100%;
    height: 100%;
}  
{% if section.settings.image_ratio == 'square' %}
.image-card-slider .card-image {
  padding-bottom: 100%;
}
.image-card-slider .card-image img {
    position: absolute;
    object-fit: cover;
} 
{% elsif section.settings.image_ratio == 'portrait'  %}
.image-card-slider .card-image {
  padding-bottom: 120%;
}
.image-card-slider .card-image img {
    position: absolute;
    object-fit: cover;
} 
{% elsif section.settings.image_ratio == 'landscape'  %}
.image-card-slider .card-image {
  padding-bottom: 60%;
}
.image-card-slider .card-image img {
    position: absolute;
    object-fit: cover;
} 
{% else %}
{% endif %}}




  
</style>


<style>
.image-section-card-carousel .flickity-button {
  display: none;
} 
.flickity-button {
    position: absolute;
    background: #121212;
    border: none;
    color: #FFF;
}
.flickity-button:hover {
    background: #121212;
    cursor: pointer;
}
.flickity-button:focus {
    outline: 0;
    box-shadow: none;
}
.image-card-slider .carousel-item {
  width: 30%;
  padding: 10px;
  border: 2px solid #241F23;
  border-radius: 10px;
  margin-right: 10px !important;
}

.image-card-slider .section-title {
  padding-bottom: 20px;
}
.image-card-slider .section-title h2 {
  font-family: var(--heading-font-family);
  font-weight: 700;
  font-size: 62px;
  line-height: 100%;
  letter-spacing: 0px;
  vertical-align: middle;
  text-transform: uppercase;
 
}
.image-card-slider .sub-title {
    display: block;
    width: 100%;
    position: relative;
  max-width: 50%;
    margin-left: auto;
}
.image-card-slider .section-description {
    max-width: 100%;
    display: flex;
    column-gap: 10px;
}
.image-card-slider .section-description::before {
  content: url(https://cdn.shopify.com/s/files/1/0878/9170/6174/files/Vector.svg?v=1750574682);
}
.image-card-slider .image-card-slider-content {
    padding-top: 30px;
}
.image-card-slider .card-title h2 {
    padding-top: 20px;
    font-family: var(--heading-font-family);
    font-weight: 700;
    font-size: 30px;
    line-height: 124%;
    letter-spacing: .4px;
    vertical-align: middle;
    text-transform: uppercase;
    
}
.image-card-slider .section-description p,
.image-card-slider .card-description p {
    font-family: Meltmino;
    font-weight: 400;
    font-size: 20px;
    line-height: 23.4px;
    letter-spacing: 0px;
    vertical-align: middle;
    text-transform: uppercase;
    color: #2A2A2A;
}


 @media only screen and (min-width: 769px) and (max-width: 1024px) {
  .image-card-slider .carousel-item {
    width: 40%;
  }
   .image-card-slider .section-description p, 
   .image-card-slider .card-description p {
    font-size: 18px;
   }
   
   
 }
  
 @media only screen and (min-width: 481px) and (max-width: 768px) {
   .image-card-slider .carousel-item {
    width: 70%;
   }
   .image-card-slider .section-description {
    max-width: 100%;
   }
   .image-card-slider .section-title h2 {
     font-size: 45px;
   }
   .image-card-slider .section-description p, 
   .image-card-slider .card-description p {
    font-size: 18px;
   }
.image-card-slider .sub-title {
    max-width: 100%;
}
 }
  
 @media only screen and (max-width: 480px) {
    .image-card-slider .carousel-item {
    width: 70%;
   }
   .image-card-slider .section-description {
    max-width: 100%;
   }
   .image-card-slider .section-title h2 {
     font-size: 30px;
   }
   .image-card-slider .section-description p, 
   .image-card-slider .card-description p {
    font-size: 12px;
   }
   .image-card-slider .card-title h2 {
     font-size: 20px;
   }
.image-card-slider .sub-title {
    max-width: 100%;
}
 }
 
</style>
{% schema %}
  {
    "name": "Image Card Slider",
    "settings": [
    {
      "type": "text",
      "id": "section_title",
      "label": "Heading"
    },
    {
      "type": "select",
      "id": "image_ratio",
      "options": [
        {
          "value": "adapt_image",
          "label": "Adapt to image"
        },
        {
          "value": "square",
          "label": "Square"
        },
        {
          "value": "portrait",
          "label": "Portrait"
        },
        {
          "value": "landscape",
          "label": "Landscape"
        }
      ],
      "label": "Image ratio",
      "default": "portrait"
    },
    {
      "type": "header",
      "content": "Section Padding"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Padding Top",
      "default": 40
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Padding Bottom",
      "default": 52
    }
    ],
    "blocks": [
    {
      "type": "card-block",
      "name": "Card Block",
      "limit": 12,
      "settings": [
        {
          "type": "image_picker",
          "id": "card_image",
          "label": "Image"
        },
        {
          "type": "text",
          "id": "card_title",
          "label": "Heading"
        },
        {
          "type": "richtext",
          "id": "card_description",
          "label": "Short Description"
        },
        {
          "type": "richtext",
          "id": "section_card_description",
          "label": "Description"
        }
      ]
    }
    ],
    "presets": [
    {
      "name": "Image Card Slider",
      "blocks": [
      {
        "type": "card-block"
      }
      ]
    }
  ]
  }
{% endschema %}