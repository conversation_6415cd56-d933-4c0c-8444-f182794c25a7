{%- style -%}
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }
{%- endstyle -%}


<script src="https://cdn.jsdelivr.net/gh/tigrr/circle-progress@v0.2.4/dist/circle-progress.min.js"></script>

<div class="scores-progres section-{{ section.id }}-padding">
  <div class="section   section-blends section-full text-custom">
    <div class="scores-progres-main">
       {% if section.settings.scores_progres_title != blank %}
          <div class="scores-progres-title">
            <h2><p>{{ section.settings.scores_progres_title }}</p></h2>
          </div>
        {% endif %}
      {% if section.settings.scores_progres_sub_title != blank %}
          <div class="scores-progres-sub-title"><p>{{ section.settings.scores_progres_sub_title }}</p></div>
        {% endif %}
      <div class="scores-progres-content">
        {% for block in section.blocks %}
          <div class="progress section-{{ section.id }}">
            <div class="progress-item progress-{{ block.id }}"></div>
            <h2>{{ block.settings.progressbar_title }}</h2>
            <script>
            var MAX = 100
            var cp = new CircleProgress('.progress-{{ block.id }}', {
            	max: MAX,
            	value: {{ block.settings.progress_value }},
            	animationDuration: 3000,
            	textFormat: (val) => val + '%<h2>{{ block.settings.progressbar_title }}</h2>',
            });
            </script>
            <div class="progress-bottom">
              <div class="progress-bottom-content">
              {% if block.settings.progress_bottom_title != blank %}
                <div class="progress-bottom-title">
                  <h2>{{ block.settings.progress_bottom_title }}</h2>
                </div>
              {% endif %}
              <div class="progress-bottom-link">
                <svg class="progress-bottom-link-plus" width="15" height="16" viewBox="0 0 15 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M14.505 6.3C14.665 6.3 14.745 6.385 14.745 6.555V8.775C14.745 8.945 14.665 9.03 14.505 9.03H9.27V14.895C9.27 15.065 9.19 15.15 9.03 15.15H6.705C6.545 15.15 6.465 15.065 6.465 14.895V9.03H1.2C1.04 9.03 0.96 8.945 0.96 8.775V6.555C0.96 6.385 1.04 6.3 1.2 6.3H6.465V0.404999C6.465 0.234999 6.545 0.149999 6.705 0.149999H9.03C9.19 0.149999 9.27 0.234999 9.27 0.404999V6.3H14.505Z" fill="#E7E8E5"/>
                </svg>
                <svg class="progress-bottom-link-minus" xmlns="http://www.w3.org/2000/svg" width="8" height="3" viewBox="0 0 8 3" fill="none">
                <path d="M0.495 2.695C0.325 2.695 0.24 2.61 0.24 2.44V0.37C0.24 0.209999 0.325 0.129999 0.495 0.129999H7.515C7.685 0.129999 7.77 0.209999 7.77 0.37V2.44C7.77 2.52 7.745 2.585 7.695 2.635C7.655 2.675 7.595 2.695 7.515 2.695H0.495Z" fill="#241F23"/>
                </svg>
              </div>
            </div>
            </div>
            <div class="progress-abs-content">
              <div class="progress-abs-main">
                {% if block.settings.progress_abs_image != blank %}
                <div class="progress-abs-image">
                  <img src="{{ block.settings.progress_abs_image | img_url:'700x' }}" alt="{{ block.settings.progress_abs_image.alt }}">
                </div>
                {% endif %}
                {% if block.settings.progress_abs_description != blank %}
                <div class="progress-abs-description">
                  {{ block.settings.progress_abs_description }}
                </div>
                {% endif %}
              </div>
            </div>
          </div>
          
        {% endfor %}
      </div>
     
    </div>
  </div>
</div>



<style>
.scores-progres path.circle-progress-value,
.scores-progres circle.circle-progress-circle {
    display: none;
}
text.circle-progress-text {
    font-family: Meltmino;
    font-weight: 800;
    fill: #2A2A2A;
    font-size: 48.92px;
}
.circle-progress {
    width: 215px;
    height: 282px;
}
.scores-progres .scores-progres-title h2 {
  font-family: var(--heading-font-family);
    font-weight: 700;
    font-size: 62px;
    line-height: 100%;
    letter-spacing: 0px;
    vertical-align: middle;
    text-transform: uppercase;
    padding-bottom: 30px;
}
.scores-progres .scores-progres-content {
    display: flex;
    justify-content: left;
    align-items: center;
    flex-wrap: wrap;
    gap: 5px;
}
.scores-progres .progress {
    width: calc(50% - 3px);
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    border-radius: 10px;
    border: 2px solid #241F23;
      cursor: pointer;
  position: relative;
  
}
svg.progress-bottom-link-minus {
    display: none;
}
.scores-progres .progress-bottom {
    display: block;
    width: 100%;
}
.scores-progres .progress-bottom-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px 20px;
    margin-top: -50px;
}
.progress-bottom-title {
    width: calc(100% - 50px);
}
.scores-progres .progress-bottom-title h2 {
  font-family: var(--heading-font-family);
  font-weight: 500;
  font-size: 30px;
  line-height: 124%;
  letter-spacing: .4px;
  vertical-align: middle;
  text-transform: uppercase;
  color: #241F23;
}
.scores-progres .progress-bottom-link {
padding: 5px;
    background: #241F23;
    border-radius: 50%;
    position: relative;
    z-index: 2;
    width: 45px;
    height: 45px;
    display: flex;
    justify-content: center;
    align-items: center;
}
.progress-abs-content {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    border: 0;
    right: 0;
background: #2A2A2A;
    z-index: 1;
    opacity: 0;
      transition: 0.3s;
}
.scores-progres .progress:hover .progress-abs-content {
    opacity: 1;
}
.scores-progres .progress:hover .progress-bottom-link {
    background: #E7E8E5;
}
.scores-progres .progress:hover .progress-bottom-link svg.progress-bottom-link-minus {
        display: block;
}
.scores-progres .progress:hover .progress-bottom-link svg.progress-bottom-link-plus {
        display: none;
}
.progress-abs-image {
    padding: 7px;
    margin-bottom: 15px;
      max-height: 206px;
}
.progress-abs-description {
font-family: var(--heading-font-family);
    font-weight: 500;
    font-size: 30px;
    line-height: 100%;
    letter-spacing: .4px;
    vertical-align: middle;
    max-width: calc(100% - 55px);
    color: #E7E8E5;
    padding: 20px;
    padding-bottom: 22px;
}
.progress-abs-image img {
    width: 100%;
    height: 100%;
}
.progress-abs-main {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    justify-content: space-between;
}
@media only screen and (min-width: 769px) and (max-width: 1024px) {
.progress-abs-description {
  font-size: 18px;
  padding: 14px;
  padding-bottom: 22px;
  padding-right: 0;
}
.scores-progres .progress-bottom-title h2 {
    font-size: 20px;
}
.scores-progres .progress-bottom-link {
    width: 31px;
    height: 32px;
}
.scores-progres .progress-bottom-content {
    padding: 0 15px 20px;
}
  
}
@media only screen and (min-width: 481px) and (max-width: 768px) {
  .scores-progres .scores-progres-title h2 {
    font-size: 45px;
  }
 .scores-progres .progress {
    width: 100%;
 }
  .progress-abs-description {
  font-size: 22px;
}
}
@media only screen and (max-width: 480px) {
  
  .scores-progres .scores-progres-title h2 {
    font-size: 30px;
  }
 .scores-progres .progress {
    width: 100%;
 }
.progress-abs-description {
  font-size: 22px;
}
}
  
</style>

{% schema %}
  {
    "name": "Scores Progress",
    "settings": [
      {
          "type": "text",
          "id": "scores_progres_title",
          "label": "Heading"
        },
      {
          "type": "text",
          "id": "scores_progres_sub_title",
          "label": "Sub Heading"
        },
       {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "padding Top",
      "default": 40
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "padding Bottom",
      "default": 52
    }
    ],
     "blocks": [
    {
      "type": "progressbar",
      "name": "Progressbar",
      "limit": 4,
      "settings": [
        {
          "type": "text",
          "id": "progress_value",
          "label": "Progressbar Value"
        },
        {
          "type": "text",
          "id": "progress_bottom_title",
          "label": "Heading"
        },
        {
          "type": "image_picker",
          "id": "progress_abs_image",
          "label": "Image"
        },
        {
          "type": "textarea",
          "id": "progress_abs_description",
          "label": "Description"
        }
      ]
    }
     ],
    "presets": [
    {
      "name": "Scores Progress",
      "blocks": [
        {
           "type": "progressbar"
        }
      ]
    }
  ]
  }
{% endschema %}